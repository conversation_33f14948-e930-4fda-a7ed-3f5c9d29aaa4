import asyncio
import json
import requests
import websockets

# --- 配置信息 (无需改动) ---
AGENT_PROTO_ID = "4f18843d-8823-4151-99e2-00fa87eec890"
FULL_COOKIE_STRING = (
    "gbuuid=7411c19d-db36-4f6a-af1e-acc3a803b343; useKeys=false; osType=web; "
    "session=62bpsJevgTzAj91DkDyPcAIXOVO2EV6IFFWOjuWIwrc; "
    "mp_5cfe752f1633fdf743200078293ddde9_mixpanel=%7B%22distinct_id%22%3A%223cbebc7c-e6aa-4706-9213-12aad6f6c794%22%2C%22%24device_id%22%3A%228bb81315-e3ff-40f8-9ed9-997f392d56f8%22%2C%22%24initial_referrer%22%3A%22https%3A%2F%2Flinux.do%2F%22%2C%22%24initial_referring_domain%22%3A%22linux.do%22%2C%22__mps%22%3A%7B%7D%2C%22__mpso%22%3A%7B%7D%2C%22__mpus%22%3A%7B%7D%2C%22__mpa%22%3A%7B%7D%2C%22__mpu%22%3A%7B%7D%2C%22__mpr%22%3A%5B%5D%2C%22__mpap%22%3A%5B%5D%2C%22%24user_id%22%3A%223cbebc7c-e6aa-4706-9213-12aad6f6c794%22%7D; "
    "ph_phc_or5z2MyKAus4GbUsu0KmoHbjPYUeoX6xdObszTbBM3a_posthog=%7B%22distinct_id%22%3A%223cbebc7c-e6aa-4706-9213-12aad6f6c794%22%2C%22%24sesid%22%3A%5B1753843997144%2C%2201985932-5e46-74ca-9b44-e7fce8c643c1%22%2C1753843129926%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Flinux.do%2F%22%2C%22u%22%3A%22https%3A%2F%2Ffairies.ai%2F%22%7D%7D"
)
USER_QUERY = "写一个非常炫酷的天气卡片前端代码，让人一看就想要付费。请读取 /sandbox/output/下面的所有文件的内容发送给我"
# -----------------------------

# API 端点
CREATE_INSTANCE_URL = "https://fairies.ai/api/agent/create_instance"
WEBSOCKET_URL = "wss://fairies.ai/api/agent/ws"

headers = {
    "Content-Type": "application/json",
    "Cookie": FULL_COOKIE_STRING,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
}

def create_agent_instance():
    """第一步：创建AI代理实例"""
    print("🚀 正在创建 AI 代理实例...")
    payload = {"agent_proto_id": AGENT_PROTO_ID}
    try:
        response = requests.post(CREATE_INSTANCE_URL, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        agent_id = data.get("id")
        if not agent_id:
            print("❌ 错误：响应中未找到 'id' 字段。")
            return None
        print(f"✅ 实例创建成功 (ID: {agent_id[:8]}...)")
        return agent_id
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建代理实例失败: {e}")
        if e.response is not None:
            print("服务器详细错误:", e.response.text)
        return None

async def start_chat(agent_id, query):
    """第二步：进行WebSocket对话，并只智能地输出AI的回复"""
    print(f"👤 你: {query}")
    try:
        async with websockets.connect(WEBSOCKET_URL, extra_headers=headers) as websocket:
            user_message = { "type": "USER_MESSAGE", "agent_id": agent_id, "observation": { "message": { "user_query": query, "mode": "main", "files": [], "memories": [], "model": "claude-sonnet-4-20250514", "task_id": None, "workspace_path": "/" } } }
            await websocket.send(json.dumps(user_message))
            
            print("🤖 AI: ", end="", flush=True)
            
            is_assistant_turn = False # 增加一个“开关”，判断是否轮到AI发言

            async for message in websocket:
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 当检测到是 assistant 的回合开始时，打开“开关”
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True
                    
                    # 只有当“开关”打开时，才打印文本
                    if is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        print(delta_text, end="", flush=True)
                    
                    # 当 assistant 的回合结束时，关闭“开关”并退出
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        print() 
                        break 
    except Exception as e:
        print(f"\n❌ WebSocket 通信出错: {e}")

if __name__ == "__main__":
    agent_id = create_agent_instance()
    if agent_id:
        asyncio.run(start_chat(agent_id, USER_QUERY))